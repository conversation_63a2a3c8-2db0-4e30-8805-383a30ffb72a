import React, { RefObject, useMemo } from "react";
import {
  Keyboard,
  StyleSheet,
  TextInput,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import { NavigationProp, ParamListBase } from "@react-navigation/native";
import {
  type Theme,
  SearchInput,
  SPACING,
  useThemeAwareObject,
  AdvancedSearchButtons,
} from "b-ui-lib";
import {
  calculateSearchFiltersCount,
  SEARCH_FIELD_NAMES,
} from "../../constants/gridMessagesSearchFields";
import { SCREEN_NAMES } from "../../constants/screenNames";
import { TEST_IDS } from "../../constants/testIds";

// Components
import { InboxSearchFields } from "../../components/inboxSearchFilters/InboxSearchFiltersScreen";
import filterSearchSuggestions from "../../helpers/filterSearchSuggestions";
import { INBOX_INITIAL_FILTERS } from "../../constants/inboxInitialFilters";

type Props = {
  navigation: NavigationProp<ParamListBase>;
  inboxSearchFields: InboxSearchFields;
  appliedSearchFilters: [];
  searchFiltersCount?: number;
  clearInboxSearchFields: () => void;
  clearFilteredEmailIds: () => void;
  setInboxSearchFields: (fields: {}) => void;
  fetchGridMessages: () => void;
  searchSuggestions: string[];
  addSearchSuggestion: (suggestion: string) => void;
  searchInputRef: RefObject<TextInput>;
};

const InboxSearchHeader = ({
  navigation,
  inboxSearchFields,
  appliedSearchFilters,
  searchFiltersCount,
  clearInboxSearchFields,
  clearFilteredEmailIds,
  setInboxSearchFields,
  fetchGridMessages,
  searchSuggestions,
  addSearchSuggestion,
  searchInputRef,
}: Props) => {
  const { styles } = useThemeAwareObject(createStyles);

  const navigateToInboxSearchFilters = () =>
    navigation.navigate(SCREEN_NAMES.inboxSearchFilters);

  const clearAll = () => {
    clearInboxSearchFields();
    clearFilteredEmailIds();
  };

  const handleFetchGridMessages = (latestSearchText: string) => {
    // When searching from search input we want to initialize filters.
    setInboxSearchFields(INBOX_INITIAL_FILTERS);

    fetchGridMessages();
    addSearchSuggestion(latestSearchText);
  };

  const handleSearchInputChange = (inputValue: string) => {
    setInboxSearchFields({
      [SEARCH_FIELD_NAMES.searchText]: inputValue,
    });
  };

  const filteredSuggestions = useMemo(
    () =>
      filterSearchSuggestions(searchSuggestions, inboxSearchFields.searchText),
    [searchSuggestions, inboxSearchFields]
  );

  const handleSearchInputClear = () => {
    clearInboxSearchFields();
    fetchGridMessages();
  };

  const handleSuggestionPress = (suggestion: string) => {
    setInboxSearchFields({
      [SEARCH_FIELD_NAMES.searchText]: suggestion,
    });

    handleFetchGridMessages(suggestion);
  };

  const searchTextFilter = appliedSearchFilters?.filter(
    (filter) => filter.FN === "MSS_TextValue"
  );

  const onOutsidePress = () => {
    if (Keyboard.isVisible()) {
      Keyboard.dismiss();
    }
  };

  return (
    <TouchableWithoutFeedback onPress={onOutsidePress}>
      <View style={styles.container}>
        <SearchInput
          testID={TEST_IDS.inboxSearchInput}
          searchInputRef={searchInputRef}
          value={inboxSearchFields?.searchText}
          containerStyle={styles.searchInput}
          onChangeText={handleSearchInputChange}
          handleDebounceFunction={handleFetchGridMessages}
          handleInputClear={handleSearchInputClear}
          placeholder={"Search here"}
          suggestions={filteredSuggestions}
          handleSuggestionPress={handleSuggestionPress}
        />

        <AdvancedSearchButtons
          searchFiltersCount={
            searchFiltersCount === 1 && searchTextFilter
              ? 0
              : searchFiltersCount
          }
          isClearAllButtonVisible={
            searchTextFilter ? searchFiltersCount > 1 : searchFiltersCount > 0
          }
          handleClearAll={clearAll}
          handlePressCriteriaButton={navigateToInboxSearchFilters}
          style={{ buttonsContainer: styles.buttonsHeader }}
        />
      </View>
    </TouchableWithoutFeedback>
  );
};

export default InboxSearchHeader;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.BACKGROUND,
    },
    row: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      padding: SPACING.S,
    },
    headerTitle: {
      fontSize: 20,
    },
    multiSelectIconsContainer: {
      flexDirection: "row",
      gap: SPACING.TEN,
      alignItems: "center",
      paddingLeft: SPACING.XS,
    },
    searchInput: {
      marginVertical: SPACING.S,
      marginHorizontal: SPACING.M,
      zIndex: 1000,
    },
    buttonsHeader: {
      padding: SPACING.M,
    },
  });

  return { styles, color };
};

import React, { useEffect, useState } from "react";
import {
  Keyboard,
  StyleSheet,
  TouchableWithoutFeedback,
  View,
} from "react-native";
import { NavigationProp, ParamListBase } from "@react-navigation/native";
import {
  type Theme,
  IconButton,
  SPACING,
  CustomText,
  useThemeAwareObject,
  Checkbox,
} from "b-ui-lib";

// Components
import BurgerMenuHOC from "../../containers/BurgerMenuHOC";
import { TEST_IDS } from "../../constants/testIds";

type Props = {
  navigation: NavigationProp<ParamListBase>;
  title: string;
  isMultiSelectActive: boolean;
  cancelMultiSelection: () => void;
  selectAllEmails: () => void;
  unSelectAllEmails: () => void;
  selectedMessageIds: string[];
  emailIds: string[];
};

const InboxNavigationHeader = ({
  title,
  isMultiSelectActive,
  cancelMultiSelection,
  selectAllEmails,
  unSelectAllEmails,
  selectedMessageIds,
  emailIds,
}: Props) => {
  const [isSelectedAll, setIsSelectedAll] = useState(false);
  const { styles, color } = useThemeAwareObject(createStyles);

  useEffect(() => {
    if (
      !isSelectedAll &&
      selectedMessageIds?.length > 0 &&
      emailIds?.length > 0 &&
      selectedMessageIds?.length === emailIds?.length
    ) {
      setIsSelectedAll(true);
    }

    if (
      isSelectedAll &&
      emailIds?.length > 0 &&
      selectedMessageIds?.length === 0
    ) {
      setIsSelectedAll(false);
    }
  }, [selectedMessageIds, emailIds]);

  const toggleCheckbox = () => {
    if (!isSelectedAll) {
      setIsSelectedAll(!isSelectedAll);
      return selectAllEmails();
    }

    if (isSelectedAll) {
      setIsSelectedAll(!isSelectedAll);
      return unSelectAllEmails();
    }
  };

  const onOutsidePress = () => {
    if (Keyboard.isVisible()) {
      Keyboard.dismiss();
    }
  };

  return (
    <TouchableWithoutFeedback onPress={onOutsidePress}>
      <View style={styles.container}>
        <View style={styles.row}>
          <BurgerMenuHOC />

          <CustomText style={styles.headerTitle}>{title}</CustomText>

          {!isMultiSelectActive && <View style={{ width: 44 }} />}

          {isMultiSelectActive && (
            <View style={styles.multiSelectIconsContainer}>
              <Checkbox
                testID={TEST_IDS.inboxNavigationHeaderSelectAllCheckbox}
                isChecked={isSelectedAll}
                onPress={toggleCheckbox}
              />

              <IconButton
                name="x"
                size={20}
                color={color.GREY3}
                onPress={cancelMultiSelection}
              />
            </View>
          )}
        </View>
      </View>
    </TouchableWithoutFeedback>
  );
};

export default InboxNavigationHeader;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      backgroundColor: color.BACKGROUND,
    },
    row: {
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      padding: SPACING.S,
    },
    headerTitle: {
      fontSize: 20,
    },
    multiSelectIconsContainer: {
      flexDirection: "row",
      gap: SPACING.TEN,
      alignItems: "center",
      paddingLeft: SPACING.XS,
    },
    searchInput: {
      marginVertical: SPACING.S,
      marginHorizontal: SPACING.M,
    },
    buttonsHeader: {
      padding: SPACING.M,
    },
  });

  return { styles, color };
};

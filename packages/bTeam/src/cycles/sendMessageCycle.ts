import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import { sendMessage, sendMessageFailed, sendMessageSuccess } from "../slices/generalSlice.ts";
import { fetchGridMessages } from "../slices/gridMessageSlice.ts";

export const postMessage = (sources) => {
  const state$ = sources.STATE;
  const token$ = state$.map((state) => state?.persist?.bTeamAuth?.token);
  const domainBaseUrl$ = state$.map((state) => state?.persist?.bTeamAuth?.domainBaseUrl);

  const request$ = sources.ACTION.filter(
    (action) => action.type === sendMessage.type)
    .compose(sampleCombine(token$, domainBaseUrl$))
    .map(([action, token, domainBaseUrl]) => {
      const { MSG_From, MSG_To, MSG_Subject, MSG_Body, MSG_Cc
        , MSG_Bcc, UNI_Guid, UMS_Guid, Attachments<PERSON><PERSON><PERSON>, AttachmentsAdded, Attach<PERSON><PERSON><PERSON><PERSON>, AttachmentsGuids, MSG_AttachmentsGuid, MessageSend } = action?.payload

      return {
        url: `${domainBaseUrl}/api/SendMessages2/${UMS_Guid}`,
        category: "postMessage",
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
        },
        send: {
          UMS_Guid,
          UNI_Guid,
          MSG_MST_Id: 1,
          MSG_From,
          MSG_To,
          MSG_Cc,
          MSG_Bcc,
          MSG_Subject,
          MSG_IsHtml: 1,
          MSG_Body,
          MSG_Importance: 2,
          MSG_AttachmentsCount: AttachmentsGuids?.length || 0,
          MSG_AttachmentsGuid,
          AttachmentsGuids,
          AttachmentsInitial,
          AttachmentsAdded,
          AttachmentsDeleted,
          MSG_RequestDeliveryReceipt: 0,
          MSG_RequestReadReceipt: 0,
          MSG_SendMailingLists: "",
          MSG_MessageActionType: 12,
          MessageSend,
          MSG_ClientApplication: 2,
          UMS_Guid_Parent: null,
        },
      };
    });

  const response$ = sources.HTTP.select("postMessage")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 201);

  const action1$ = xs
    .combine(response$)
    .map(([response]) => {
      const responseBody = response?.body;

      return sendMessageSuccess(responseBody);
    });

  const action2$ = xs
    .combine(response$)
    .map(([response]) => {

      return fetchGridMessages();
    })

  return {
    ACTION: xs.merge(action1$, action2$),

    HTTP: request$,
  };
};

export const postMessageFailed = (sources) => {
  const response$ = sources.HTTP.select("postMessage")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 201);

  const action$ = xs
    .combine(response$)
    .map((arr) => sendMessageFailed(arr));

  return {
    ACTION: action$,
  };
};

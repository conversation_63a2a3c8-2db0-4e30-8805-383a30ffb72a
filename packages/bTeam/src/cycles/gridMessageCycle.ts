import xs from "xstream/index";
import sampleCombine from "xstream/extra/sampleCombine";
import { GridMessageResponseDTO } from "../types/DTOs/GridMessageResponseDTO";
import { ResponseDTO } from "../types/DTOs/ResponseDTO";
import {
  fetchGridMessageFailed,
  fetchGridMessages,
  fetchGridMessageSuccess,
} from "../slices/gridMessageSlice";
import { calculateInboxSearchFieldsCount } from "../constants/gridMessagesSearchFields";
import { FOLDER_NAMES } from "../constants/folderNames";
import { checkIfStringValueExists } from "../helpers/checkIfStringValueExists";
import { parseJsonFilterCriteria } from "../helpers/parseJsonFilterCriteria";

export const getGridMessages = (sources) => {
  const state$ = sources.STATE;
  const token$ = state$.map((state) => state?.persist?.bTeamAuth?.token);
  const domainBaseUrl$ = state$.map((state) => state?.persist?.bTeamAuth?.domainBaseUrl);
  const selectedFolderId$ = state$.map(
    (state) => state?.persist?.gridMessageSlice?.selectedFolderId
  );
  const inboxSearchFields$ = state$.map(
    (state) => state?.root?.bTeamGeneralSlice?.inboxSearchFields
  );
  const actionPayload$ = sources.ACTION.filter(
    (action) => action.type === fetchGridMessages.type
  );
  const folders$ = state$.map(
    (state) => state?.persist?.gridMessageSlice?.folders
  );

  const request$ = sources.ACTION.filter(
    (action) => action.type === fetchGridMessages.type
  )
    .compose(
      sampleCombine(token$, selectedFolderId$, inboxSearchFields$, folders$, domainBaseUrl$)
    )
    .map(([action, token, selectedFolderId, inboxSearchFields, folders, domainBaseUrl]) => {
      const { pageSize, skipFirst } = action.payload || {};

      const allFolderId = folders.allIds.find(
        (folderId: string) => folders.byId[folderId].name === FOLDER_NAMES.all
      );

      const {
        inOut,
        searchText,
        searchIn,
        from,
        to,
        datePeriod,
        flagged,
        hasAttachments,
        readUnread,
      } = inboxSearchFields || {};

      const hasSearchFilters =
        calculateInboxSearchFieldsCount(inboxSearchFields) > 0;

      const jsonFilterCriteria = {
        Type: 2,
        Fields: [
          {
            FN: "FLD_Guids_String",
            // We only search in ALL folders not matter what the user has chose in burger menu
            FV: allFolderId,
            FO: 18,
          },
          ...(checkIfStringValueExists(datePeriod)
            ? [
              {
                FN: "MSS_TimePeriod",
                FV: datePeriod,
                FO: 4,
              },
            ]
            : []),
          ...(checkIfStringValueExists(hasAttachments)
            ? [
              {
                FN: "MSS_Attachments",
                FV: hasAttachments,
                FO: 11,
              },
            ]
            : []),
          ...(checkIfStringValueExists(readUnread)
            ? [
              {
                FN: "MSS_Read",
                FV: readUnread,
                FO: 17,
              },
            ]
            : []),
          ...(checkIfStringValueExists(flagged)
            ? [
              {
                FN: "MSS_Flagged",
                FV: flagged,
                FO: 15,
              },
            ]
            : []),
          ...(checkIfStringValueExists(inOut)
            ? [
              {
                FN: "MSS_InOut",
                FV: inOut,
                FO: 1,
              },
            ]
            : []),
          ...(checkIfStringValueExists(from)
            ? [
              {
                FN: "MSS_From",
                FV: from,
                FO: 7,
              },
            ]
            : []),
          ...(checkIfStringValueExists(to)
            ? [
              {
                FN: "MSS_To",
                FV: to,
                FO: 8,
              },
            ]
            : []),
          ...(checkIfStringValueExists(searchText)
            ? [
              {
                FN: "MSS_TextValue",
                FV: searchText,
                FO: 9,
              },
            ]
            : []),
          ...(checkIfStringValueExists(searchIn)
            ? [
              {
                FN: "MSS_TextIn",
                FV: searchIn,
                FO: 10,
              },
            ]
            : []),
        ],
      };

      if (selectedFolderId) {
        const queryParameters = new URLSearchParams();

        if (!hasSearchFilters) {
          queryParameters.append("FLD_Guid", selectedFolderId);
        }

        queryParameters.append("messageBodyTextFirstChars", "200");
        queryParameters.append("$inlinecount", "allpages");
        queryParameters.append("$top", (pageSize || 100).toString());
        queryParameters.append("$skip", (skipFirst || 0).toString());
        queryParameters.append("$orderby", "MSG_CreatedDateTime desc");

        if (hasSearchFilters) {
          queryParameters.append(
            "jsonFilterCriteria",
            JSON.stringify(jsonFilterCriteria)
          );
        }

        return {
          url: `${domainBaseUrl}/odt/GridMessages2?${queryParameters.toString()}`,
          category: "getGridMessages",
          method: "GET",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        };
      }
    });

  const response$ = sources.HTTP.select("getGridMessages")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status === 200);

  const action$ = response$
    .compose(sampleCombine(actionPayload$, selectedFolderId$))
    .map(([response, actionPayload, selectedFolderId]) => {
      const { skipFirst } = actionPayload?.payload || {};
      const responseBody = response?.body as unknown as ResponseDTO<
        GridMessageResponseDTO[]
      >;

      return fetchGridMessageSuccess({
        responseBody,
        folderGuid: selectedFolderId,
        loadMore: skipFirst,
        searchFilters: parseJsonFilterCriteria(response?.request.url),
      });
    });

  const failedActionStream$ = sources.ACTION.filter(
    (action) => action.type === fetchGridMessages.type
  )
    .compose(sampleCombine(state$))
    .map(([action, state]) => {
      const selectedFolderId =
        state?.persist?.gridMessageSlice?.selectedFolderId;
      if (!selectedFolderId) {
        return fetchGridMessageFailed({
          requestMessage: "Missing selected folder",
        });
      } else {
        return { type: "noAction" };
      }
    });

  return {
    ACTION: xs.merge(action$, failedActionStream$),
    HTTP: request$,
  };
};

export const getGridMessagesFailed = (sources) => {
  const response$ = sources.HTTP.select("getGridMessages")
    .map((response) => response.replaceError((err) => xs.of(err)))
    .flatten()
    .filter((response) => response.status !== 200);

  const action$ = xs
    .combine(response$)
    .map((arr) => fetchGridMessageFailed(arr));
  return {
    ACTION: action$,
  };
};

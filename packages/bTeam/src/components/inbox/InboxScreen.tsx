import React, { ReactElement, type RefObject } from "react";
import {
  LayoutChangeEvent,
  RefreshControl,
  StyleSheet,
  View,
  ViewStyle,
} from "react-native";
import {
  NavigationProp,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native";
import {
  type Theme,
  useThemeAwareObject,
  MessageList,
  SPACING,
  IconButton,
  CustomText,
} from "b-ui-lib";
import { SCREEN_NAMES } from "../../constants/screenNames";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import type { BottomSheetMethods } from "@gorhom/bottom-sheet/lib/typescript/types";
import { BottomSheetMenuState } from "../../containers/InboxHOC";
import { Message } from "../../types/message";

// Components
import InboxBottomSheet from "./inboxBottomSheet/InboxBottomSheet";
import {TEST_IDS} from "../../constants/testIds";

export type BottomSheetAction = {
  title: string;
  onPress: () => void;
  icon: string;
  testID: string;
  isDisabled?: boolean;
};

type Props = {
  sections: Array<{
    title: string;
    data: Message[];
  }>;
  isHeaderVisible: boolean;
  onScrollToHideHeader: () => void;
  onScrollToRevealHeader: () => void;
  isMultiSelectActive: boolean;
  selectedMessageIds: string[];
  onLongTapToSelectEmail: (id: string) => void;
  onTapToSelectAdditionalEmail: (id: string) => void;
  onDeselectEmail: (id: string) => void;
  onSelectAllEmails: () => void;
  onClearAllSelections: () => void;
  onCancelMultiSelection: () => void;
  onFlagPress: (messageId: string) => void;
  handleTapMessage: (message: Message) => void;
  handleRefreshList: () => void;
  isLoading: boolean;
  bottomSheetRef: RefObject<BottomSheetMethods>;
  bottomSheetMenuState: BottomSheetMenuState;
  setBottomSheetMenuState: (menuState: BottomSheetMenuState) => void;
  bottomSheetOptions: BottomSheetAction[];
  bottomSheetMoreOptions: BottomSheetAction[];
  errorMessage: string;
  emptyMessage: string;
  loadMoreEmails: () => void;
  listFooterComponent: ReactElement;
  initialNumToRender: number;
  onScroll: Function;
  inboxListStyle: ViewStyle | ViewStyle[];
  onLayout: (event: LayoutChangeEvent) => void;
};

const InboxScreen = ({
  sections,
  isHeaderVisible,
  onScrollToHideHeader,
  onScrollToRevealHeader,
  isMultiSelectActive,
  selectedMessageIds,
  onLongTapToSelectEmail,
  onTapToSelectAdditionalEmail,
  onDeselectEmail,
  onClearAllSelections,
  onCancelMultiSelection,
  onFlagPress,
  handleTapMessage,
  handleRefreshList,
  isLoading,
  bottomSheetRef,
  bottomSheetMenuState,
  setBottomSheetMenuState,
  bottomSheetOptions,
  bottomSheetMoreOptions,
  errorMessage,
  emptyMessage,
  loadMoreEmails,
  listFooterComponent,
  initialNumToRender,
  onScroll,
  listStyle,
  onLayout,
}: Props) => {
  const navigation = useNavigation<NavigationProp<ParamListBase>>();
  const { styles, color } = useThemeAwareObject(createStyles);

  const handleComposeIconPress = () =>
    navigation.navigate(SCREEN_NAMES.composeMessage);

  const handleLongTapToSelectEmail = (messageId: string) => {
    bottomSheetRef.current?.snapToIndex(0);
    onLongTapToSelectEmail(messageId);
  };

  if (errorMessage) {
    return (
      <View style={styles.container}>
        <CustomText style={styles.errorMessage}>{errorMessage}</CustomText>
      </View>
    );
  }

  return (
    <GestureHandlerRootView style={styles.container}>
      <View style={[styles.container]}>
        <MessageList
          onScroll={onScroll}
          style={listStyle}
          onLayout={onLayout}
          sections={sections}
          emptyText={emptyMessage}
          isSkeletonLoading={isLoading}
          isMultiSelectActive={isMultiSelectActive}
          selectedMessagesIds={selectedMessageIds}
          handleTapMessage={handleTapMessage}
          handleLongTapToSelectEmail={handleLongTapToSelectEmail}
          handleTapToSelectAdditionalEmail={onTapToSelectAdditionalEmail}
          handleDeselectMessage={onDeselectEmail}
          handleFlagPress={onFlagPress}
          loadMoreEmails={loadMoreEmails}
          listFooterComponent={listFooterComponent}
          initialNumToRender={initialNumToRender}
          isHeaderVisible={isHeaderVisible}
          scrollToHideHeader={onScrollToHideHeader}
          scrollToRevealHeader={onScrollToRevealHeader}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={() => handleRefreshList()}
              tintColor={color.BLACK}
            />
          }
        />
      </View>

      <IconButton
        testID={TEST_IDS.inboxComposeIcon}
        name="edit"
        size={22}
        color={color.WHITE}
        onPress={handleComposeIconPress}
        containerStyle={styles.composeIcon}
      />

      <InboxBottomSheet
        bottomSheetRef={bottomSheetRef}
        bottomSheetMenuState={bottomSheetMenuState}
        setBottomSheetMenuState={setBottomSheetMenuState}
        bottomSheetDefaultActions={bottomSheetOptions}
        bottomSheetMoreActions={bottomSheetMoreOptions}
        isMultiSelectActive={isMultiSelectActive}
        handleUnSelectAllMessages={onClearAllSelections}
        handleCancelMultiSelection={onCancelMultiSelection}
      />
    </GestureHandlerRootView>
  );
};

export default InboxScreen;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
    },
    errorMessage: {
      color: color.ERROR,
      alignSelf: "center",
      paddingTop: SPACING.L,
    },
    listFooterComponent: {
      height: 50,
    },
    composeIcon: {
      backgroundColor: color.BRAND_DEFAULT,
      padding: SPACING.M,
      borderRadius: 30,
      position: "absolute",
      bottom: 30,
      right: "5%",
    },
  });

  return { styles, color };
};

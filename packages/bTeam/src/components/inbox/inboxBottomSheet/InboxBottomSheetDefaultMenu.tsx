import React, { type FC } from "react";
import { StyleSheet, View } from "react-native";
import {
  Button,
  BUTTON_DEFAULT_VARIANTS,
  type Theme,
  useThemeAwareObject,
} from "b-ui-lib";
import { BottomSheetAction } from "../InboxScreen";

type Props = {
  bottomSheetDefaultActions: BottomSheetAction[];
};

export const InboxBottomSheetDefaultMenu: FC<Props> = ({
  bottomSheetDefaultActions,
}) => {
  const { styles } = useThemeAwareObject((theme) => createStyles(theme));

  return (
    <View style={styles.container}>
      {bottomSheetDefaultActions.map((option, index) => (
        <Button
          key={`${index} ${option.title}`}
          testID={option.testID}
          title={option.title}
          onPress={() => option?.onPress && option.onPress()}
          variant={BUTTON_DEFAULT_VARIANTS.secondary}
          textStyle={styles.button}
          isDisabled={option.isDisabled}
        />
      ))}
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flexDirection: "row",
      justifyContent: "space-between",
    },
    button: {
      color: color.MESSAGE_FLAG,
    },
  });

  return { styles, color };
};

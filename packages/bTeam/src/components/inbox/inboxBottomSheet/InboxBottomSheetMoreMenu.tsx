import React, { type FC } from "react";
import { StyleSheet, View } from "react-native";
import {
  type Theme,
  FONT_SIZES,
  IconTextButton,
  SPACING,
  useThemeAwareObject,
} from "b-ui-lib";
import { BottomSheetAction } from "../InboxScreen";

type Props = {
  bottomSheetMoreActions: BottomSheetAction[];
};

export const InboxBottomSheetMoreMenu: FC<Props> = ({
  bottomSheetMoreActions,
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);

  return (
    <View style={styles.container}>
      {bottomSheetMoreActions.map((option, index) => (
        <View style={styles.iconTextButtonContainer}>
          <IconTextButton
            key={`${index} ${option.title}`}
            testID={option.testID}
            iconName={option.icon}
            iconColor={color.MESSAGE_FLAG}
            title={option.title}
            onPress={option.onPress}
            textStyle={styles.iconText}
            containerStyle={styles.iconTextButton}
            isDisabled={option.isDisabled}
          />
        </View>
      ))}
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      paddingHorizontal: SPACING.M,
    },
    iconTextButtonContainer: {
      paddingVertical: SPACING.L,
      paddingHorizontal: SPACING.S,
      borderBottomWidth: 1,
      borderBottomColor: color.INVERTED_HALF_DIMMED,
    },
    iconTextButton: {
      justifyContent: "flex-start",
    },
    iconText: {
      color: color.BRAND_BLUE,
      fontSize: FONT_SIZES.SIXTEEN,
      fontWeight: "700",
    },
  });

  return { styles, color };
};

import React, { useState } from "react";
import { View, StyleSheet, Pressable } from "react-native";
import {
  CustomText,
  FONT_SIZES,
  IconButton,
  SPACING,
  Theme,
  useThemeAwareObject,
} from "b-ui-lib";

// Type for attachment objects that include both GUID and name
type AttachmentItem = {
  FLN_Guid: string;
  documentName: string;
};

type Props = {
  messageAttachments: AttachmentItem[];
  handleRemoveAttachment: (index: number) => void;
};

const AttachmentsSection: React.FC<Props> = ({
  messageAttachments,
  handleRemoveAttachment,
}) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const [isExpanded, setIsExpanded] = useState(false);

  if (!messageAttachments || messageAttachments.length === 0) {
    return null;
  }

  const maxVisibleAttachments = 2;
  const visibleAttachments = isExpanded
    ? messageAttachments
    : messageAttachments.slice(0, maxVisibleAttachments);
  const hasMoreAttachments = messageAttachments.length > maxVisibleAttachments;

  const renderAttachmentItem = (attachment: AttachmentItem, index: number) => (
    <View key={attachment.FLN_Guid} style={styles.attachmentItem}>
      <View style={styles.attachmentContent}>
        <IconButton
          name="paperclip"
          size={16}
          color={color.TEXT_DEFAULT}
          style={styles.attachmentIcon}
        />
        <CustomText style={styles.attachmentName} numberOfLines={1}>
          {attachment.documentName}
        </CustomText>
      </View>
      <IconButton
        name="x"
        size={16}
        color={color.TEXT_DEFAULT}
        onPress={() => handleRemoveAttachment(index)}
        style={styles.removeButton}
      />
    </View>
  );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <CustomText style={styles.title}>
          Attachments ({messageAttachments.length})
        </CustomText>

        {hasMoreAttachments && (
          <Pressable
            onPress={() => setIsExpanded(!isExpanded)}
            style={styles.expandButton}
          >
            <CustomText style={styles.expandText}>
              {isExpanded
                ? "Show less"
                : `Show all (${messageAttachments.length})`}
            </CustomText>
            <IconButton
              name={isExpanded ? "chevron-up" : "chevron-down"}
              size={14}
              color={color.TEXT_DIMMED}
            />
          </Pressable>
        )}
      </View>

      <View style={styles.attachmentsList}>
        {visibleAttachments.map((attachment, index) =>
          renderAttachmentItem(attachment, index)
        )}
      </View>
    </View>
  );
};

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      marginTop: SPACING.XS,
      marginBottom: SPACING.XS,
      paddingVertical: SPACING.XS,
      backgroundColor: color.MESSAGE_ITEM__BACKGROUND,
      borderRadius: SPACING.SIX,
    },
    header: {
      flexDirection: "row",
      justifyContent: "space-between",
      alignItems: "center",
      marginBottom: SPACING.XS,
    },
    title: {
      fontSize: FONT_SIZES.TWELVE,
      color: color.TEXT_DEFAULT,
      fontWeight: "600",
    },
    expandButton: {
      flexDirection: "row",
      alignItems: "center",
    },
    expandText: {
      fontSize: FONT_SIZES.TEN,
      color: color.TEXT_DIMMED,
      marginRight: SPACING.XXS,
    },
    attachmentsList: {
      flexWrap: "wrap",
      gap: SPACING.XS,
    },
    attachmentItem: {
      flex: 0.5,
      flexDirection: "row",
      alignItems: "center",
      justifyContent: "space-between",
      paddingVertical: SPACING.XS,
      paddingHorizontal: SPACING.XS,
      backgroundColor: color.PRESSABLE,
      borderRadius: SPACING.XS,
    },
    attachmentContent: {
      flexDirection: "row",
      alignItems: "center",
      flex: 1,
    },
    attachmentIcon: {},
    attachmentName: {
      fontSize: FONT_SIZES.TWELVE,
      color: color.TEXT_DEFAULT,
      marginLeft: SPACING.XS,
    },
    removeButton: {
      marginLeft: SPACING.XS,
    },
  });

  return { styles, color };
};

export default AttachmentsSection;

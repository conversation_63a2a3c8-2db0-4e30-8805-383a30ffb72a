import React, { useCallback, useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON>Hand<PERSON> } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { useNavigation, useRoute } from "@react-navigation/native";
import { uploadAttachmentHelper } from "../helpers/postAttachment";
import {
  convertPlainTextToHtml,
  convertHtmlToPlainText,
} from "../helpers/textFormatting";

// Components
import ComposeMessageScreen from "../components/composeMessage/ComposeMessageScreen";

// Actions
import {
  sendMessage,
  sendMessageClearStatus,
  getDraftMessage,
  sendDraftMessage,
} from "../slices/generalSlice";

// Types
import { DraftMessageDTO } from "../types/DTOs/DraftMessageDTO";
import { SCREEN_NAMES } from "../constants/screenNames";
import { MessageActionTypes } from "../types/MessageActionTypes";
import { getAttachments } from "../slices/attachmentsSlice";

type Props = {};

const ComposeMessageHOC: React.FC<Props> = () => {
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const route = useRoute();

  // Local state flag to indicate we're in the process of exiting.
  const [isExiting, setIsExiting] = useState<boolean>(false);
  const [isNavigatingAfterSend, setIsNavigatingAfterSend] = useState(false);

  // Get UMS_Guid from route params if provided.
  const UMS_Guid = route.params?.UMS_Guid;
  const messageType = route.params?.messageType || "New";

  // If UMS_Guid exists, select the corresponding grid message from Redux.
  const gridMessage: any = useSelector((state: any) =>
    UMS_Guid ? state.persist.gridMessageSlice.gridMessages.byId[UMS_Guid] : null
  );

  const { userUniboxes, userEmailAdresses } = useSelector(
    (state: any) => state.persist.bTeamUsers
  );
  const { token, domainBaseUrl } = useSelector(
    (state: any) => state.persist.bTeamAuth
  );
  const {
    sendMessageLoading,
    sendMessageError,
    sendMessageSuccess,
    sendDraftMessageLoading,
    sendDraftMessageError,
    sendDraftMessageSuccess,
  } = useSelector((state: any) => state.root.bTeamGeneralSlice);
  const draftMessage: DraftMessageDTO = useSelector(
    (state: any) => state.root.bTeamGeneralSlice.draftMessage
  );
  const attachments = useSelector(
    (state: any) => state.root.bTeamAttachmentsSlice.attachments
  );

  useEffect(() => {
    if (draftMessage && messageType !== MessageActionTypes.DraftEdit) {
      setSelectedRecipientEmailId(draftMessage.UNI_Guid || "");
      setToRecipients(draftMessage.Tos?.map((r) => r.EmailAddress) || []);
      setCcRecipients(draftMessage.Ccs?.map((r) => r.EmailAddress) || []);
      setBccRecipients(draftMessage.Bccs?.map((r) => r.EmailAddress) || []);
      setSubject(draftMessage.MSG_Subject || "");

      // Attachments are already an array of GUIDs
      const attachmentGuids = draftMessage.Attachments || [];
      setmessageAttachments(attachmentGuids);
      setInitialAttachments(attachmentGuids);
      setAddedAttachments([]);
      setDeletedAttachments([]);

      // Always convert the content to ensure line breaks are preserved
      // Use fullMessageBody instead of MSG_Body to ensure we get the complete content with preserved line breaks
      const bodyContent =
        draftMessage.fullMessageBody || draftMessage.MSG_Body || "";
      const plainTextBody = convertHtmlToPlainText(bodyContent);

      setEmailSignature(plainTextBody);
    }
  }, [draftMessage]);

  const [selectedRecipientEmailId, setSelectedRecipientEmailId] =
    useState<string>("");

  // "To" Field State
  const [toRecipients, setToRecipients] = useState<string[]>([]);
  const [toInput, setToInput] = useState<string>("");

  // "Cc" Field State
  const [ccRecipients, setCcRecipients] = useState<string[]>([]);
  const [ccInput, setCcInput] = useState<string>("");

  // "Bcc" Field State
  const [bccRecipients, setBccRecipients] = useState<string[]>([]);
  const [bccInput, setBccInput] = useState<string>("");

  const [subject, setSubject] = useState<string>("");
  const [emailContent, setEmailContent] = useState<string>("");

  const [emailSignature, setEmailSignature] = useState("");

  // A simple email validation helper.
  const validateEmail = (email: string) => {
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return emailRegex.test(email);
  };

  // Compute validation flags.
  const isToInputValid = toInput.length === 0 || validateEmail(toInput);
  const isCcInputValid = ccInput.length === 0 || validateEmail(ccInput);
  const isBccInputValid = bccInput.length === 0 || validateEmail(bccInput);

  // ----- Handlers for the "To" Field -----
  const handleToInputChange = (text: string) => {
    if (text.endsWith(",") || text.endsWith(" ")) {
      const email = text.slice(0, -1).trim();
      if (validateEmail(email) && email.length > 0) {
        setToRecipients((prev) => [...prev, email]);
      }
      setToInput("");
    } else {
      setToInput(text);
    }
  };

  const removeToRecipient = (index: number) => {
    setToRecipients((prev) => prev.filter((_, i) => i !== index));
  };

  // ----- Handlers for the "Cc" Field -----
  const handleCcInputChange = (text: string) => {
    if (text.endsWith(",") || text.endsWith(" ")) {
      const email = text.slice(0, -1).trim();
      if (validateEmail(email) && email.length > 0) {
        setCcRecipients((prev) => [...prev, email]);
      }
      setCcInput("");
    } else {
      setCcInput(text);
    }
  };

  const removeCcRecipient = (index: number) => {
    setCcRecipients((prev) => prev.filter((_, i) => i !== index));
  };

  // ----- Handlers for the "Bcc" Field -----
  const handleBccInputChange = (text: string) => {
    if (text.endsWith(",") || text.endsWith(" ")) {
      const email = text.slice(0, -1).trim();
      if (validateEmail(email) && email.length > 0) {
        setBccRecipients((prev) => [...prev, email]);
      }
      setBccInput("");
    } else {
      setBccInput(text);
    }
  };

  const removeBccRecipient = (index: number) => {
    setBccRecipients((prev) => prev.filter((_, i) => i !== index));
  };

  // ----- Handler for Removing an Attachment (Scenario 5) -----
  const handleRemoveAttachment = (index: number) => {
    const attachmentToRemove = messageAttachments[index];

    // Remove from current attachments
    setmessageAttachments((prev) => prev.filter((_, i) => i !== index));

    // If it was an initial attachment, add to deleted list
    if (initialAttachments.includes(attachmentToRemove)) {
      setDeletedAttachments((prev) => [...prev, attachmentToRemove]);
    }

    // If it was a newly added attachment, remove from added list
    if (addedAttachments.includes(attachmentToRemove)) {
      setAddedAttachments((prev) =>
        prev.filter((guid) => guid !== attachmentToRemove)
      );
    }
  };

  const [uploadAttachmentLoading, setUploadAttachmentLoading] =
    useState<boolean>(false);
  const [uploadAttachmentError, setUploadAttachmentError] =
    useState<string>("");
  const [messageAttachments, setmessageAttachments] = useState<string[]>([]);
  const [initialAttachments, setInitialAttachments] = useState<string[]>([]);
  const [addedAttachments, setAddedAttachments] = useState<string[]>([]);
  const [deletedAttachments, setDeletedAttachments] = useState<string[]>([]);
  const [totalAttachmentSize, setTotalAttachmentSize] = useState(0);

  // In the attachment helper, use the grid message's MSG_Guid if available.
  const handleAddAttachment = async () => {
    setUploadAttachmentLoading(true);
    setUploadAttachmentError("");

    // Use gridMessage.MSG_Guid if available; otherwise, use draftMessage.MSG_Guid.
    const effectiveMsgGuid =
      UMS_Guid && gridMessage ? gridMessage.MSG_Guid : draftMessage.MSG_Guid;

    await uploadAttachmentHelper(
      token,
      effectiveMsgGuid,
      1001, // Message attachment type
      totalAttachmentSize,
      (FLN_Guid: string, documentSize: number, documentName: string) => {
        const newTotalSize = totalAttachmentSize + documentSize;
        setmessageAttachments((prev) => [...prev, FLN_Guid]);
        setAddedAttachments((prev) => [...prev, FLN_Guid]);
        setTotalAttachmentSize(newTotalSize);
        setUploadAttachmentLoading(false);
      },
      (error: string) => {
        setUploadAttachmentLoading(false);
        setUploadAttachmentError(error);
      },
      domainBaseUrl,
      () => setUploadAttachmentLoading(false)
    );
  };

  // When sending a message.
  const handleSendMessage = useCallback(() => {
    if (!subject) {
      Alert.alert(
        "Subject is empty",
        "Please make sure the Subject field is filled.",
        [{ text: "OK" }]
      );
      return;
    }
    const fromEmail = userUniboxes.byId[selectedRecipientEmailId]?.value;
    const toString = toRecipients.join("; ");
    const ccString = ccRecipients.join("; ");
    const bccString = bccRecipients.join("; ");

    const effectiveUMSGuid = draftMessage.UMS_Guid;
    const effectiveMsgAttachmentsGuid = draftMessage.MSG_Guid;

    // Convert plain text to HTML-friendly format to preserve line breaks
    const formattedEmailContent = convertPlainTextToHtml(emailContent);

    dispatch(
      sendMessage({
        UNI_Guid: selectedRecipientEmailId,
        MSG_From: fromEmail,
        MSG_To: toString,
        MSG_Subject: subject,
        MSG_Body: formattedEmailContent,
        MSG_Cc: ccString,
        MSG_Bcc: bccString,
        UMS_Guid: effectiveUMSGuid,
        AttachmentsInitial: initialAttachments,
        AttachmentsAdded: addedAttachments,
        AttachmentsDeleted: deletedAttachments,
        MSG_AttachmentsGuid: effectiveMsgAttachmentsGuid,
        AttachmentsGuids: messageAttachments,
        MessageSend: true,
      })
    );
  }, [
    dispatch,
    selectedRecipientEmailId,
    userUniboxes,
    toRecipients,
    ccRecipients,
    bccRecipients,
    subject,
    emailContent,
    draftMessage,
    gridMessage,
    UMS_Guid,
    messageAttachments,
    initialAttachments,
    addedAttachments,
    deletedAttachments,
  ]);

  // When saving the draft.
  const handleSendDraftMessage = useCallback(() => {
    if (!selectedRecipientEmailId) {
      Alert.alert(
        "From field empty",
        "Please make sure the 'From:' value is selected.",
        [{ text: "OK" }]
      );
      return;
    }
    const fromEmail = userUniboxes.byId[selectedRecipientEmailId]?.value;
    const toString = toRecipients.join("; ");
    const ccString = ccRecipients.join("; ");
    const bccString = bccRecipients.join("; ");

    const effectiveUMSGuid = draftMessage.UMS_Guid;
    const effectiveMsgAttachmentsGuid = draftMessage.MSG_Guid;

    // Convert plain text to HTML-friendly format to preserve line breaks
    const formattedEmailContent = convertPlainTextToHtml(emailContent);

    dispatch(
      sendDraftMessage({
        UNI_Guid: selectedRecipientEmailId,
        MSG_From: fromEmail,
        MSG_To: toString,
        MSG_Subject: subject,
        MSG_Body: formattedEmailContent,
        MSG_Cc: ccString,
        MSG_Bcc: bccString,
        UMS_Guid: effectiveUMSGuid,
        AttachmentsInitial: initialAttachments,
        AttachmentsAdded: addedAttachments,
        AttachmentsDeleted: deletedAttachments,
        MSG_AttachmentsGuid: effectiveMsgAttachmentsGuid,
        AttachmentsGuids: messageAttachments,
        MessageSend: false,
      })
    );
  }, [
    dispatch,
    selectedRecipientEmailId,
    userUniboxes,
    toRecipients,
    ccRecipients,
    bccRecipients,
    subject,
    emailContent,
    draftMessage,
    gridMessage,
    UMS_Guid,
    messageAttachments,
    initialAttachments,
    addedAttachments,
    deletedAttachments,
  ]);

  useEffect(() => {
    if (!sendMessageLoading) {
      if (sendMessageError) {
        Alert.alert(
          "Something went wrong",
          "Please make sure all fields are properly filled.",
          [{ text: "OK" }]
        );
      } else if (sendMessageSuccess) {
        setIsNavigatingAfterSend(true); // Set the flag to true before navigating.
        Alert.alert("Success", "Message sent successfully.", [
          {
            text: "OK",
            onPress: () => {
              navigation.navigate(SCREEN_NAMES.inbox as never);
              dispatch(sendMessageClearStatus()); // Reset success state.
              setIsNavigatingAfterSend(false); // Reset the flag after navigation.
            },
          },
        ]);
      }
    }
  }, [
    sendMessageLoading,
    sendMessageError,
    sendMessageSuccess,
    navigation,
    dispatch,
  ]);

  useEffect(() => {
    if (!sendDraftMessageLoading) {
      if (sendDraftMessageError) {
        Alert.alert(
          "Something went wrong",
          "Please make sure all fields are properly filled.",
          [{ text: "OK" }]
        );
      } else if (sendDraftMessageSuccess) {
        setIsNavigatingAfterSend(true); // Prevent subsequent alerts.
        Alert.alert("Success", "Draft saved successfully.", [
          {
            text: "OK",
            onPress: () => {
              navigation.navigate(SCREEN_NAMES.inbox as never);
              dispatch(sendMessageClearStatus()); // Clear success state.
              setIsNavigatingAfterSend(false);
            },
          },
        ]);
      }
    }
  }, [
    sendDraftMessageLoading,
    sendDraftMessageError,
    sendDraftMessageSuccess,
    navigation,
    dispatch,
    isExiting,
    isNavigatingAfterSend,
  ]);

  useEffect(() => {
    dispatch(getDraftMessage({ messageType, UMS_Guid }));
  }, [dispatch]);

  useEffect(() => {
    return () => {
      // Reset success states when the component unmounts.
      dispatch(sendMessageClearStatus());
    };
  }, [dispatch]);

  // -------------------------------
  // Prefill draft if UMS_Guid is provided
  // -------------------------------
  useEffect(() => {
    if (
      UMS_Guid &&
      gridMessage &&
      messageType === MessageActionTypes.DraftEdit
    ) {
      setEmailSignature("");
      setSubject(gridMessage.subject || "");

      // Convert HTML body to plain text to preserve line breaks when editing
      // Use fullMessageBody instead of body to ensure we get the complete content with preserved line breaks
      const bodyContent = gridMessage.fullMessageBody || "";

      // Always convert the content to ensure line breaks are preserved
      // This handles both HTML content and plain text with <br> tags
      const plainTextBody = convertHtmlToPlainText(bodyContent);

      // Ensure we're setting the content with line breaks preserved
      setEmailContent(plainTextBody);

      // Pre-fill "To" recipients.
      if (gridMessage.to && gridMessage.to.trim().length > 0) {
        setToRecipients(
          gridMessage.to
            .split(";")
            .map((email: string) => email.trim())
            .filter((email: string) => email)
        );
      } else if (gridMessage.tos && gridMessage.tos.length > 0) {
        setToRecipients(
          gridMessage.tos.map((recipient: any) => recipient.EmailAddress)
        );
      }

      // Pre-fill "Cc" recipients.
      if (gridMessage.cc && gridMessage.cc.trim().length > 0) {
        setCcRecipients(
          gridMessage.cc
            .split(";")
            .map((email: string) => email.trim())
            .filter((email: string) => email)
        );
      } else if (gridMessage.ccs && gridMessage.ccs.length > 0) {
        setCcRecipients(
          gridMessage.ccs.map((recipient: any) => recipient.EmailAddress)
        );
      }

      // Pre-fill "Bcc" recipients.
      if (gridMessage.bcc && gridMessage.bcc.trim().length > 0) {
        setBccRecipients(
          gridMessage.bcc
            .split(";")
            .map((email: string) => email.trim())
            .filter((email: string) => email)
        );
      } else if (gridMessage.bccs && gridMessage.bccs.length > 0) {
        setBccRecipients(
          gridMessage.bccs.map((recipient: any) => recipient.EmailAddress)
        );
      }

      // Preselect the "From" value by matching gridMessage.from.
      if (gridMessage.from) {
        const uniboxes = Object.values(userUniboxes.byId);
        const matched = uniboxes.find(
          (box: any) => box.value === gridMessage.from
        ) as any; // Cast to any to avoid TypeScript errors
        if (matched && matched.id) {
          setSelectedRecipientEmailId(matched.id);
        }
      }

      // Pre-fill attachments if available.
      if (
        gridMessage.attachmentsIds &&
        Array.isArray(gridMessage.attachmentsIds) &&
        gridMessage.attachmentsIds.length > 0
      ) {
        setmessageAttachments(gridMessage.attachmentsIds);
        setInitialAttachments(gridMessage.attachmentsIds);
        setAddedAttachments([]);
        setDeletedAttachments([]);
      }
    }
  }, [UMS_Guid, gridMessage, userUniboxes]);

  useEffect(() => {
    navigation.setOptions({
      sendMessage: handleSendMessage,
      sendDraft: handleSendDraftMessage,
      addAttachment: handleAddAttachment,
      // Pass down the number of attachments and the remove handler for the UI.
      uploadAttachmentLoading: uploadAttachmentLoading,
      messageAttachmentsLength: messageAttachments.length,
      removeAttachment: handleRemoveAttachment,
    });
  }, [
    navigation,
    selectedRecipientEmailId,
    toRecipients,
    subject,
    emailContent,
    ccRecipients,
    bccRecipients,
    draftMessage,
    uploadAttachmentLoading,
    messageAttachments,
  ]);

  useEffect(() => {
    const unsubscribe = navigation.addListener("beforeRemove", (e) => {
      if (isExiting || isNavigatingAfterSend) {
        // Allow navigation if we're already exiting or navigating after a send/save.
        return;
      }

      // Prevent the default behavior of leaving the screen.
      e.preventDefault();

      // Display the confirmation prompt.
      Alert.alert(
        "Unsaved Changes",
        "Do you want to Discard changes, Cancel, or Save before leaving?",
        [
          {
            text: "Discard",
            style: "destructive",
            onPress: () => {
              setIsExiting(true);
              navigation.dispatch(e.data.action);
            },
          },
          {
            text: "Cancel",
            style: "cancel",
            onPress: () => {
              // Do nothing, stay on the screen.
            },
          },
          {
            text: "Save",
            onPress: () => {
              setIsExiting(true);
              handleSendDraftMessage();
              navigation.dispatch(e.data.action);
            },
          },
        ]
      );
    });

    return unsubscribe;
  }, [navigation, isExiting, isNavigatingAfterSend, handleSendDraftMessage]);

  return (
    <ComposeMessageScreen
      userUniboxes={userUniboxes}
      userEmailAdresses={userEmailAdresses}
      selectedRecipientEmailId={selectedRecipientEmailId}
      setSelectedRecipientEmailId={setSelectedRecipientEmailId}
      emailToSend={toInput}
      toRecipients={toRecipients}
      handleEmailChange={handleToInputChange}
      subject={subject}
      setSubject={setSubject}
      emailContent={emailContent}
      setEmailContent={setEmailContent}
      ccEmail={ccInput}
      ccRecipients={ccRecipients}
      handleEmailCcChange={handleCcInputChange}
      bccEmail={bccInput}
      bccRecipients={bccRecipients}
      handleEmailBccChange={handleBccInputChange}
      isEmailValid={isToInputValid}
      isCcEmailValid={isCcInputValid}
      isBccEmailValid={isBccInputValid}
      isLoading={sendMessageLoading || sendDraftMessageLoading}
      removeToRecipient={removeToRecipient}
      removeCcRecipient={removeCcRecipient}
      removeBccRecipient={removeBccRecipient}
      messageBody={emailSignature}
      messageAttachments={messageAttachments}
      handleRemoveAttachment={handleRemoveAttachment}
    />
  );
};

export default ComposeMessageHOC;

import React, { useEffect } from "react";
import { StyleSheet, View, Platform } from "react-native";
import { useDispatch, useSelector } from "react-redux";
import { getDomainBaseUrl, getCompanyGroup, logout } from "../slices/authSlice";
import {
  getTrackingStatus,
  requestTrackingPermission,
} from "react-native-tracking-transparency";

// Components
import { ActivityIndicator } from "react-native";
import TabsNavigation from "../navigation/TabsNavigaton";

// Styles
import { useThemeAwareObject, Theme, CustomText, Button } from "b-ui-lib";

type Props = {};

/**
 * Checks and requests App Tracking Transparency permission on iOS
 */
const checkATTPermission = async () => {
  // Only run on iOS devices
  if (Platform.OS === "ios") {
    const trackingStatus = await getTrackingStatus();
    console.log("App Tracking Transparency status:", trackingStatus);

    if (trackingStatus === "not-determined") {
      const permission = await requestTrackingPermission();
      if (permission === "authorized") {
        console.log("User granted tracking permission ✅");
      } else {
        console.log("User denied tracking permission ❌");
      }
    }
  }
};

const UrlParamsHOC = ({}: Props) => {
  const { styles, color } = useThemeAwareObject(createStyles);
  const dispatch = useDispatch();
  const {
    isDomainBaseUrlLoading,
    isDomainBaseUrlError,
    domainBaseUrlError,
    isCompanyGroupLoading,
    isCompanyGroupError,
    companyGroupError,
    domainBaseUrl,
  } = useSelector((state: any) => state.persist.bTeamAuth);

  const _getDomainBaseUrl = () => {
    dispatch(getDomainBaseUrl());
  };

  useEffect(() => {
    setTimeout(() => {
      _getDomainBaseUrl();
    }, 1);
    setTimeout(() => {
      dispatch(getCompanyGroup());
    }, 2);

    // Check ATT permission with a slight delay to not interfere with initial loading
    setTimeout(() => {
      checkATTPermission();
    }, 1000);
  }, []);

  const _returnToLogin = () => {
    dispatch(logout());
  };

  if (isDomainBaseUrlLoading || isCompanyGroupLoading || !domainBaseUrl) {
    return (
      <View style={styles.container}>
        <ActivityIndicator
          size="large"
          color={color.MESSAGE_FLAG}
          style={{ paddingBottom: 20 }}
        />

        {!domainBaseUrl && <Button title="Retry" onPress={_getDomainBaseUrl} />}
      </View>
    );
  }

  if (isDomainBaseUrlError) {
    return (
      <View style={styles.container}>
        <CustomText style={styles.text}>{domainBaseUrlError}</CustomText>

        <Button title="< Login" onPress={_returnToLogin} />
      </View>
    );
  }

  if (isCompanyGroupError) {
    return (
      <View style={styles.container}>
        <CustomText style={styles.text}>{companyGroupError}</CustomText>

        <Button title="< Login" onPress={_returnToLogin} />
      </View>
    );
  }

  return <TabsNavigation />;
};

export default UrlParamsHOC;

const createStyles = ({ color }: Theme) => {
  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: color.BACKGROUND,
      justifyContent: "center",
      alignItems: "center",
    },
    text: {
      color: color.TEXT_DEFAULT,
      paddingBottom: 20,
    },
  });

  return { styles, color };
};
